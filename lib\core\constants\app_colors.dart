import 'package:flutter/material.dart';

class AppColors {
  // Background colors - Updated gradient colors
  static const Color gradientStart = Color(0xFF5C493B);
  static const Color gradientEnd = Color(0xFF202020);
  static const Color backgroundColor = Color(0xFF202020);
  static const Color cardBackground = Color(0xFF2A2A2A);
  static const Color cardBackgroundLight = Color(0xFF3A3A3A);

  // Text colors
  static const Color textPrimary = Color(0xFFFFFFFF);
  static const Color textSecondary = Color(0xFFB0B0B0);
  static const Color textTertiary = Color(0xFF808080);

  // Accent colors
  static const Color primaryAccent = Color(0xFFD4A574);
  static const Color connectButton = Color(0xFFD4A574);

  // Score indicator colors
  static const Color nourishment = Color(0xFF4ECDC4);
  static const Color movement = Color(0xFFD4A574);
  static const Color rest = Color(0xFFFF6B6B);
  static const Color mindfulness = Color(0xFF95E1D3);
  static const Color hydration = Color(0xFF9B59B6);

  // Activity colors
  static const Color activityCard = Color(0xFF2A2A2A);

  // Recovery colors
  static const Color recoveryBackground = Color(0xFF1E3A5F);
  static const Color recoveryProgress = Color(0xFF4A90E2);

  // Daily tags colors
  static const Color tagBackground = Color(0xFF3A3A3A);
  static const Color tagText = Color(0xFFB0B0B0);

  // Bottom navigation
  static const Color bottomNavBackground = Color(0xFFFFFFFF);
  static const Color bottomNavSelected = Color(0xFF1A1A1A);
  static const Color bottomNavUnselected = Color(0xFF808080);
  static const Color addButtonBackground = Color(0xFFD4A574);

  // Diet page colors
  static const Color pieChartVeggies = Color(0xFFD4A574);
  static const Color pieChartProtein = Color(0xFF808080);
  static const Color pieChartCarbs = Color(0xFF5C493B);
  static const Color chatBubbleUser = Color(0xFFD4A574);
  static const Color chatBubbleBot = Color(0xFF3A3A3A);
  static const Color inputBackground = Color(0xFF3A3A3A);
  static const Color micButtonBackground = Color(0xFFD4A574);
}
